<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProfessionalsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize, WithTitle
{
    protected $users;
    protected $type;

    public function __construct($users, $type = 'all')
    {
        $this->users = $users;
        $this->type = $type;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $baseHeadings = [
            'Name',
            'Email',
            'Phone',
            'Status',
            'Approval Status',
            'Role',
            'Joined Date',
            'Total Bookings',
            'Profile Completed',
            'Email Verified',
            'City',
            'Country',
            'State',
            'Address',
            'Postal Code',
            'Company Name',
            'Website',
            'Facebook',
            'Instagram',
            'TikTok',
            'VAT Number',
            'Company ID',
            'Gender',
            'Date of Birth',
            'Bio',
            'Location Service',
            'Rating'
        ];

        return $baseHeadings;
    }

    /**
     * @param mixed $user
     * @return array
     */
    public function map($user): array
    {
        return [
            $user->name ?? 'N/A',
            $user->email ?? 'N/A',
            $user->profile->phone ?? 'N/A',
            $user->status == 1 ? 'Active' : 'Inactive',
            $user->approval == 1 ? 'Approved' : 'Pending',
            $user->roles->first()->name ?? 'N/A',
            $user->created_at ? $user->created_at->format('M d, Y') : 'N/A',
            isset($user->bookings) ? $user->bookings->count() : 0,
            $user->registration_completed == 1 ? 'Yes' : 'No',
            $user->email_verified_at ? 'Yes' : 'No',
            $user->profile->city ?? 'N/A',
            $user->profile->country ?? 'N/A',
            $user->profile->state ?? 'N/A',
            $user->profile->address ?? 'N/A',
            $user->profile->postal ?? 'N/A',
            $user->profile->company_name ?? 'N/A',
            $user->profile->website ?? 'N/A',
            $user->profile->facebook ?? 'N/A',
            $user->profile->instagram ?? 'N/A',
            $user->profile->tiktok ?? 'N/A',
            $user->profile->vat_number ?? 'N/A',
            $user->profile->company_id ?? 'N/A',
            $user->profile->gender ?? 'N/A',
            $user->profile->dob ? date('M d, Y', strtotime($user->profile->dob)) : 'N/A',
            $user->profile->bio ? strip_tags($user->profile->bio) : 'N/A',
            $user->profile->location_service == 1 ? 'Yes' : 'No',
            '5.0' // Static rating as shown in the view
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true, 'size' => 12]],

            // Set background color for header (A1 to AA1 to cover all columns)
            'A1:AA1' => [
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return ucfirst($this->type) . ' Professionals';
    }
}
