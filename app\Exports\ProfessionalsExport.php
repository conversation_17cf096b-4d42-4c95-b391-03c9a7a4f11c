<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProfessionalsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize, WithTitle
{
    protected $users;
    protected $type;

    public function __construct($users, $type = 'all')
    {
        $this->users = $users;
        $this->type = $type;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $baseHeadings = [
            'Name',
            'Email Address',
            'Subscription',
            'Status',
            'Joined Date',
            'Total Bookings'
        ];

        return $baseHeadings;
    }

    /**
     * @param mixed $user
     * @return array
     */
    public function map($user): array
    {
        return [
            $user->name ?? 'N/A',
            $user->email ?? 'N/A',
            ucfirst($user->roles->first()->name ?? ''),
            $user->status == 1 ? 'Active' : 'Inactive',
            $user->created_at ? $user->created_at->format('M d, Y') : 'N/A',
            isset($user->bookings) ? $user->bookings->count() : 0,
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true, 'size' => 12]],

            // Set background color for header (A1 to F1 for 6 columns)
            'A1:F1' => [
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return ucfirst($this->type) . ' Professionals';
    }
}
