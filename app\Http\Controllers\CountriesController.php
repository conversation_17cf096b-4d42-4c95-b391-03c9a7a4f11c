<?php

namespace App\Http\Controllers;

use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CountriesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $countries = Country::all();
        return view('dashboard.admin.countries.index', compact('countries'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'country_code' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
                'errors' => $errors
            ]);
        }
        try {
            DB::beginTransaction();
            $countryData = $validator->validated();
            Country::create($countryData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Country Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $country = Country::find($id);
        return response()->json($country);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'country_code' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $country = Country::find($id);
            $countryData = $validator->validated();
            $country->update($countryData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Country updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $country = Country::find($id);
        $country->delete();
        return response()->json(["type" => "success", "title" => "Deleted", "message" => 'Country deleted Successfully!!']);
    }
}
