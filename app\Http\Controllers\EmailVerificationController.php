<?php

namespace App\Http\Controllers;

use App\Mail\EmailVerificationMail;
use App\Mail\UserPasswordMail;
use App\Models\EmailVerification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class EmailVerificationController extends Controller
{
    public function sendOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => 'required|email',
            'user_type' => 'required|in:customer,professional',
        ]);
        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }

        $email = $request->email;
        $selectedUserType = $request->user_type;

        // Check if user already exists
        $existingUser = \App\Models\User::where('email', $email)->first();

        if ($existingUser) {
            // Email exists - check if user has the correct role
            $userRole = $existingUser->getRoleNames()->first();

            if(in_array($userRole, ["individual", "business","professional"]) || $selectedUserType == "professional"){
            } elseif($userRole == "customer" && $selectedUserType == "customer"){
            } else{
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }

            // Email exists with correct role - this is a login scenario
            return api_response(true, "Email found. Please enter your password.", [
                'scenario' => 'login',
                'user_exists' => true,
                'user_role' => $userRole
            ]);
        }

        // Email doesn't exist - this is a registration scenario, send OTP
        $plainToken = rand(100000, 999999);
        $encryptedToken = encrypt($plainToken);
        EmailVerification::updateOrCreate(
            ['email' => $email],
            [
                'token' => $encryptedToken,
                'expires_at' => now()->addMinutes(5),
            ]
        );
        Mail::to($email)->send(new EmailVerificationMail($plainToken));
        return api_response(true, "OTP Sent", [
            'scenario' => 'register',
            'user_exists' => false
        ]);
    }

    public function verifyOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => 'required|email|exists:email_verifications,email',
            'otp' => 'required|digits:6',
            "user_type" => 'required|in:customer,professional',
        ]);
        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }
        $record = EmailVerification::where('email', $request->email)->first();

        if (!$record) {
            return api_response(false, "OTP not found");
        }

        if (Carbon::parse($record->expires_at)->isPast()) {
            return api_response(false, "OTP expired");
        }

        try {
            $decryptedToken = decrypt($record->token);
        } catch (\Exception $e) {
            return api_response(false, "Invalid OTP token");
        }
        if ($decryptedToken != $request->otp) {
            return api_response(false, "Invalid OTP");
        }
        $record->delete();

        $password = Str::random(8);
        $user = new User();
        $user->name = "-";
        $user->email = $request->email;
        $user->password = Hash::make($password);
        $user->email_verified_at = now();
        $user->save();

        $role = Role::where("name",$request->user_type)->first();
        $user->assignRole($role);
        Auth::login($user);
        Mail::to($user->email)->send(new UserPasswordMail($password));
        return api_response(true, "OTP verified successfully", ["url" => route('register.user_type', ["user_type" => $request->user_type])]);
    }
}
