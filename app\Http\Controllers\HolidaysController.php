<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Holiday;
use App\Http\Requests\HolidayRequest;
use App\Models\Country;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HolidaysController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:holidays-list|holidays-create|holidays-edit|holidays-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:holidays-create', ['only' => ['create', 'store', 'import', 'downloadTemplate']]);
        $this->middleware('permission:holidays-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:holidays-delete', ['only' => ['destroy']]);
        $this->middleware('permission:holidays-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $holidays = Holiday::all();
        $countries = Country::all();
        return view('dashboard.admin.holidays.holidays', [
            'holidays' => $holidays,
            'countries' => $countries
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('holidays.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  HolidayRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(HolidayRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'country_id' => 'required',
            'date' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $holidayData = $validator->validated();
            Holiday::create($holidayData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Holiday Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return view('holidays.show', ['holiday' => $holiday]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return response()->json($holiday);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  HolidayRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(HolidayRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'status' => 'required',
            'country_id' => 'required',
            'date' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $holiday = Holiday::where('ids', $id)->firstOrFail();
            $holidayData = $validator->validated();
            $holiday->update($holidayData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Holiday updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        $holiday->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Holiday deleted successfully'
        ]);
    }

    /**
     * Import holidays from Excel/CSV file
     *
     * @param  Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:2048',
            'country_id' => 'required|exists:countries,id',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());

            if ($request->ajax()) {
                return response()->json([
                    'type' => 'error',
                    'message' => $errors,
                    'title' => 'Validation Errors',
                ], 422);
            }

            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }

        try {
            DB::beginTransaction();

            $file = $request->file('import_file');
            $countryId = $request->country_id;
            $importedCount = 0;
            $errors = [];

            // Handle CSV files
            if ($file->getClientOriginalExtension() === 'csv') {
                $importedCount = $this->importFromCsv($file, $countryId, $errors);
            } else {
                // Handle Excel files (.xlsx, .xls)
                $importedCount = $this->importFromExcel($file, $countryId, $errors);
            }

            DB::commit();

            if (!empty($errors)) {
                $errorMessage = 'Import completed with some errors:<br>' . implode('<br>', $errors);
                $response = [
                    'type' => 'warning',
                    'title' => 'Import Completed with Warnings',
                    'message' => $errorMessage . '<br>Successfully imported: ' . $importedCount . ' holidays'
                ];

                if ($request->ajax()) {
                    return response()->json($response);
                }

                return redirect()->back()->with($response);
            }

            $response = [
                'type' => 'success',
                'title' => 'Import Successful',
                'message' => 'Successfully imported ' . $importedCount . ' holidays!'
            ];

            if ($request->ajax()) {
                return response()->json($response);
            }

            return redirect()->back()->with($response);

        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'type' => 'error',
                'message' => 'Import failed: ' . $th->getMessage(),
                'title' => 'Import Error'
            ];

            if ($request->ajax()) {
                return response()->json($response, 500);
            }

            return redirect()->back()->with($response);
        }
    }

    /**
     * Import holidays from CSV file
     */
    private function importFromCsv($file, $countryId, &$errors)
    {
        $importedCount = 0;
        $handle = fopen($file->getRealPath(), 'r');

        if ($handle !== false) {
            $isFirstRow = true;
            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                // Skip header row
                if ($isFirstRow) {
                    $isFirstRow = false;
                    continue;
                }

                if (count($data) >= 2) {
                    $name = trim($data[0]);
                    $date = trim($data[1]);
                    $statusText = isset($data[2]) ? trim($data[2]) : 'active';

                    // Convert status text to 0/1
                    $status = $this->convertStatusToNumber($statusText);

                    if (!empty($name) && !empty($date)) {
                        // Validate and normalize date format
                        $normalizedDate = $this->validateAndNormalizeDate($date);
                        if ($normalizedDate) {
                            // Check if holiday already exists
                            $existingHoliday = Holiday::where('name', $name)
                                                   ->where('date', $normalizedDate)
                                                   ->where('country_id', $countryId)
                                                   ->first();

                            if (!$existingHoliday) {
                                Holiday::create([
                                    'name' => $name,
                                    'date' => $normalizedDate,
                                    'country_id' => $countryId,
                                    'status' => $status
                                ]);
                                $importedCount++;
                            } else {
                                $errors[] = "Holiday '{$name}' on {$normalizedDate} already exists - skipped";
                            }
                        } else {
                            $errors[] = "Invalid date format for '{$name}': {$date} - skipped";
                        }
                    }
                }
            }
            fclose($handle);
        }

        return $importedCount;
    }

    /**
     * Import holidays from Excel file (basic implementation)
     */
    private function importFromExcel($file, $countryId, &$errors)
    {
        // For now, we'll treat Excel files as CSV
        // This is a basic implementation - for full Excel support, Laravel Excel package is recommended
        $errors[] = "Excel files are currently processed as CSV. For full Excel support, please save as CSV format.";
        return $this->importFromCsv($file, $countryId, $errors);
    }

    /**
     * Validate and normalize date format to Y-m-d
     */
    private function validateAndNormalizeDate($date)
    {
        // More flexible date formats including single digit dates
        $formats = [
            'Y-m-d', 'Y/m/d', 'd-m-Y', 'd/m/Y', 'm-d-Y', 'm/d/Y',
            'n/j/Y', 'j/n/Y', 'Y/n/j', 'Y-n-j', 'n-j-Y', 'j-n-Y'
        ];

        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            if ($dateTime && $dateTime->format($format) == $date) {
                // Return normalized date in Y-m-d format
                return $dateTime->format('Y-m-d');
            }
        }

        // Try to parse common date formats more flexibly
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }

        return false;
    }

    /**
     * Convert status text to number (0 = inactive, 1 = active)
     */
    private function convertStatusToNumber($statusText)
    {
        $statusText = strtolower(trim($statusText));

        // Handle various active status formats
        if (in_array($statusText, ['active', '1', 'yes', 'true', 'enabled', 'on'])) {
            return 1;
        }

        // Handle various inactive status formats
        if (in_array($statusText, ['inactive', '0', 'no', 'false', 'disabled', 'off'])) {
            return 0;
        }

        // Default to active if unclear
        return 1;
    }

    /**
     * Download sample template for import
     */
    public function downloadTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="holidays_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, ['Holiday Name', 'Date (YYYY-MM-DD)', 'Status (1=active, 0=inactive)']);

            // Add sample data
            fputcsv($file, ['New Year Day', '2024-01-01', '1']);
            fputcsv($file, ['Independence Day', '2024-07-04', '1']);
            fputcsv($file, ['Christmas Day', '2024-12-25', '1']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
