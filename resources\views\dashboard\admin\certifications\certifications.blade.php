@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Product Certifications</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('certifications-create')
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-certification">
                            <i class="fa-solid fa-plus me-3"></i> Add Product Certification
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                        </div>
                        <div class="row row-gap-5 mt-5">
                            @forelse ($certifications as $certification)
                                <div class="col-lg-6">
                                    <div class="card flex-row justify-content-center align-items-center p-3 gap-5">
                                        <div class="card-header border-0 p-0 justify-content-center align-items-center">
                                            <img src="{{ asset('website') . '/' . $certification->image ?? '' }}"
                                                class="h-35px w-35px  object-fit-contain top-rated-image" alt="card-image">
                                        </div>
                                        <div class="card-body p-0">
                                            <p class="fs-16 sora w-700 m-0 dark-blue">{{ $certification->name ?? '' }}</p>
                                        </div>
                                        <div class="card-footer p-0 border-0">
                                            <div class="dropdown">
                                                <a class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </a>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('certifications-edit')
                                                        <li>
                                                            <button
                                                                class="dropdown-item complete fs-14 regular edit-certification "
                                                                type="button" data-id="{{ $certification->ids }}">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Edit
                                                            </button>
                                                        </li>
                                                    @endcan
                                                    @can('certifications-delete')
                                                        <li>
                                                            <form
                                                                action="{{ route('certifications.destroy', $certification->ids) }}"
                                                                method="POST">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button class="dropdown-item cancel fs-14 regular"
                                                                    type="submit">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @endcan
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-lg-12">
                                    <div class="card flex-row justify-content-center align-items-center p-3 gap-5">
                                        <div class="card-body p-0">
                                            <p class="fs-16 sora w-700 m-0 dark-blue">No Certifications Found</p>
                                        </div>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.certifications.modal.add-certification-modal')
    @include('dashboard.admin.certifications.modal.edit-certification-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#certificationForm").validate({
                rules: {
                    avatar: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                },
                messages: {
                    avatar: {
                        required: "Please upload an image"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('.edit-certification').click(function() {
                var certificationId = $(this).data('id');
                $.ajax({
                    url: '/certifications/' + certificationId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#certificationId').val(data.ids);
                        $('#edit-name').val(data.name);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-certification').modal('show');
                    },
                });
            });

            $('#editCertificationForm').on('submit', function(e) {
                e.preventDefault();
                var certificationId = $('#certificationId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/certifications/' + certificationId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-certification').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });
    </script>
@endpush
