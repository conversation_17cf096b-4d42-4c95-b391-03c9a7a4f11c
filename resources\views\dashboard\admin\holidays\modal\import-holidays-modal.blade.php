<!-- Import Holidays Modal -->
<div class="modal fade" id="import-holidays" tabindex="-1" aria-labelledby="importHolidaysModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importHolidaysModalLabel">Import Holidays</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="importHolidaysForm" action="{{ route('holidays.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="import_file" class="form-label form-input-labels">Select Excel/CSV File</label>
                            <input type="file" class="form-control form-inputs-field" id="import_file" name="import_file" 
                                   accept=".xlsx,.xls,.csv" required>
                            <div class="form-text">
                                <small class="text-muted">
                                    Supported formats: Excel (.xlsx, .xls) and CSV (.csv)
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="import_country_id" class="form-label form-input-labels">Default Country</label>
                            <select class="form-select form-select-field" id="import_country_id" name="country_id" 
                                    data-control="select2" data-placeholder="Select Country" required>
                                <option selected disabled>Select Country</option>
                                @foreach ($countries as $country)
                                    <option value="{{ $country->id }}">{{ $country->name }}</option>
                                @endforeach
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    This country will be applied to all imported holidays
                                </small>
                            </div>
                        </div>

                        <div class="col-md-12 mb-3">
                            <div class="alert alert-info">
                                <h6><i class="fa-solid fa-info-circle me-2"></i>File Format Requirements:</h6>
                                <ul class="mb-0">
                                    <li>Column 1: Holiday Name</li>
                                    <li>Column 2: Date (YYYY-MM-DD format)</li>
                                    <li>Column 3: Status (optional - defaults to 'active')</li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-12 mb-3">
                            <a href="{{ route('holidays.download-template') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fa-solid fa-download me-2"></i>Download Sample Template
                            </a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-solid fa-upload me-2"></i>Import Holidays
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
