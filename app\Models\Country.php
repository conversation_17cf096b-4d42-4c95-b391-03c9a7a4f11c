<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'flag',
        'country_code',
    ];

    /**
     * Boot method to automatically set flag URL when saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($country) {
            if ($country->country_code) {
                $country->flag = 'https://flagcdn.com/w320/' . strtolower($country->country_code) . '.png';
            }
        });
    }

    public function vatmanagements()
    {
        return $this->hasMany(VatManagement::class);
    }
}
