@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Countries</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('certifications-create')
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-certification">
                            <i class="fa-solid fa-plus me-3"></i> Add Country
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                        </div>
                        <div class="row row-gap-5 mt-5">
                            @foreach ($countries as $country)
                                <div class="col-lg-6">
                                    <div class="card flex-row justify-content-center align-items-center p-3 gap-5">
                                        <div class="card-header border-0 p-0 justify-content-center align-items-center">
                                            <img src="{{ $country->flag }}"
                                                class="h-35px w-35px  object-fit-contain top-rated-image" alt="card-image">
                                        </div>
                                        <div class="card-body p-0">
                                            <p class="fs-16 sora w-700 m-0 dark-blue">{{ $country->name ?? '' }}</p>
                                        </div>
                                        <div class="card-footer p-0 border-0">
                                            <div class="dropdown">
                                                <a class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </a>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('countries-edit')
                                                        <li>
                                                            <button
                                                                class="dropdown-item complete fs-14 regular edit-country "
                                                                type="button" data-id="{{ $country->id }}">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Edit
                                                            </button>
                                                        </li>
                                                    @endcan
                                                    @can('countries-delete')
                                                        <li>
                                                            <form action="{{ route('countries.destroy', $country->id) }}"
                                                                method="POST">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button class="dropdown-item cancel fs-14 regular"
                                                                    type="submit">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @endcan
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.countries.modal.add-countries-modal')
    @include('dashboard.admin.countries.modal.edit-countries-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#countriesForm").validate({
                rules: {
                    name: {
                        required: true
                    },
                    country_code: {
                        required: true
                    },
                },
                messages: {
                    name: {
                        required: "Please enter country name"
                    },
                    country_code: {
                        required: "Please enter country code"
                    },
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('.edit-country').click(function() {
                var countryId = $(this).data('id');
                $.ajax({
                    url: '/countries/' + countryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#countryId').val(data.id);
                        $('#edit-name').val(data.name);
                        $('#edit-country-code').val(data.country_code);
                        $('#edit-countries').modal('show');
                    },
                });
            });

            $('#editCountriesForm').on('submit', function(e) {
                e.preventDefault();
                var countryId = $('#countryId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/countries/' + countryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-certification').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });
    </script>
@endpush
