<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                    <h6 class="sora black">Professionals</h6>
                    <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="professionals-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-professionals" type="button" role="tab"
                                aria-controls="pills-professionals" aria-selected="true">
                                All Professionals
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="requests-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-requests" type="button" role="tab" aria-controls="pills-requests"
                                aria-selected="false">
                                New Requests
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-professionals" role="tabpanel"
                            aria-labelledby="professionals-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Select with dots -->
                                    <div class="dropdown search_box select-box">
                                        <button
                                            class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span><span class="dot"></span>
                                                All</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                    data-color="#4B5563"><span class="dot all"></span>
                                                    All</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                    data-color="#F59E0B"><span class="dot ongoing"></span>
                                                    Ongoing</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                    data-color="#3B82F6"><span class="dot upcoming"></span>
                                                    Upcoming</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                    data-color="#10B981"><span class="dot completed"></span>
                                                    Complete</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                    data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                    Canceled</a></li>
                                        </ul>
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <button class="search_input fs-14 normal link-gray ">
                                         Export  Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                </button>
                                    </div>
                                </div>

                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Email Address</th>
                                            <th>Category</th>
                                            <th>Bookings</th>
                                            <th>Action</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $approved_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $approved_user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <div class="card flex-row shadow-none p-0 gap-3 align-items-center">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="<?php echo e(asset('website') . "/" . $approved_user->profile->avatar); ?>"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0 ">
                                                            <p class="fs-16 regular black m-0 pb-5"><?php echo e($approved_user->name); ?></p>
                                                            <p class="fs-14 semi_bold sora black m-0"><i
                                                                    class="fa-solid fa-star"></i> 5.0 </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td data-label="EMAIL ADDRESS"><?php echo e($approved_user->email); ?></td>
                                                <td data-label="SUBSCRIPTION"> Individual </td>
                                                <?php if($approved_user->status == 1): ?>
                                                    <td data-label="STATUS" class="professional-status status paid-status">
                                                        Active
                                                    </td>
                                                <?php else: ?>
                                                    <td data-label="STATUS" class="professional-status status unpaid-status">
                                                        Inactive
                                                    </td>
                                                <?php endif; ?>
                                                <td data-label="JOINED DATE"><?php echo e($approved_user->created_at->format('M d, Y')); ?></td>
                                                <td data-label="TOTAL BOOKINGS"><?php echo e($approved_user?->bookings?->count() ?? 0); ?></td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <li>
                                                                <button class="dropdown-item complete fs-14 regular "
                                                                    type="button">
                                                                    <i class="bi bi-check-circle complete-icon"></i>
                                                                    Mark as Complete
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item complete fs-14 regular " href="<?php echo e(route('professional.change_status', $approved_user->ids)); ?>" >
                                                                    <i class="bi bi-check-circle complete-icon"></i>
                                                                    Change Status
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <button class="dropdown-item cancel fs-14 regular"
                                                                    type="button">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-requests" role="tabpanel" aria-labelledby="requests-tab"
                            tabindex="1">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Select with dots -->
                                    <div class="dropdown search_box select-box">
                                        <button
                                            class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span><span class="dot"></span>
                                                All</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                    data-color="#4B5563"><span class="dot all"></span>
                                                    All</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                    data-color="#F59E0B"><span class="dot ongoing"></span>
                                                    Ongoing</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                    data-color="#3B82F6"><span class="dot upcoming"></span>
                                                    Upcoming</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                    data-color="#10B981"><span class="dot completed"></span>
                                                    Complete</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                    data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                    Canceled</a></li>
                                        </ul>
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <a href="#!" class="search_input fs-14 normal link-gray ">
                                            Export Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                        </a>
                                    </div>

                                </div>
                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Email Address</th>
                                            <th>Category</th>
                                            
                                            <th>Action</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $unapproved_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unapproved_user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <div class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="<?php echo e(asset('website') . "/" . $unapproved_user->profile->avatar); ?>"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 regular black m-0 pb-5"><?php echo e($unapproved_user->name); ?></p>
                                                            <p class="fs-14 semi_bold sora black m-0"><i
                                                                    class="fa-solid fa-star"></i> 5.0 </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td data-label="EMAIL ADDRESS"><?php echo e($unapproved_user->email); ?></td>
                                                
                                                <td data-label="STATUS" class="professional-status status paid-status"><?php echo e($unapproved_user->approval == 0 ? 'Pending' : 'Approved'); ?>

                                                </td>
                                                <td data-label="JOINED DATE"><?php echo e($unapproved_user->created_at->format('M d, Y')); ?></td>
                                                <td data-label="TOTAL BOOKINGS"><?php echo e($unapproved_user?->bookings?->count() ?? 0); ?></td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <li>
                                                                <button class="dropdown-item complete fs-14 regular "
                                                                    type="button">
                                                                    <i class="bi bi-check-circle complete-icon"></i>
                                                                    Mark as Complete
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item complete fs-14 regular " href="<?php echo e(route('professional.approve', $unapproved_user->ids)); ?>" >
                                                                    <i class="bi bi-check-circle complete-icon"></i>
                                                                    Approve
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <button class="dropdown-item cancel fs-14 regular"
                                                                    type="button">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                            
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/professional/index.blade.php ENDPATH**/ ?>