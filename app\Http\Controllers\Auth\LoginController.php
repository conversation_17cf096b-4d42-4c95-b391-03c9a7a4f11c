<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Get the post-login redirect path based on user role.
     *
     * @return string
     */
    public function redirectPath()
    {
        if (auth()->check()) {
            $user = auth()->user();

            if ($user->hasRole('developer')) {
                return '/home';
            } elseif ($user->hasRole('customer')) {
                return '/';
            } elseif ($user->hasAnyRole(['admin', 'business', 'individual', 'professional'])) {
                return '/dashboard';
            }
        }

        return $this->redirectTo;
    }

    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(\Illuminate\Http\Request $request)
    {
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        if ($response = $this->authenticated($request, $this->guard()->user())) {
            return $response;
        }

        // Role-based redirection
        $user = $this->guard()->user();

        if ($user->hasRole('developer')) {
            $redirectUrl = '/home';
        } elseif ($user->hasRole('customer')) {
            $redirectUrl = '/';
        } elseif ($user->hasAnyRole(['admin', 'business', 'individual', 'professional'])) {
            $redirectUrl = '/dashboard';
        } else {
            $redirectUrl = $this->redirectPath();
        }

        return $request->wantsJson()
                    ? new \Illuminate\Http\JsonResponse([], 204)
                    : redirect()->intended($redirectUrl);
    }
}
