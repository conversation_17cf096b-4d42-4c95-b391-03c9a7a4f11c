<div class="modal fade card-details" id="edit-holiday" tabindex="-1" aria-labelledby="edit-holiday" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black">
                    Edit Holiday</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editHolidayForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <input type="hidden" id="holidayId" value="" />
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-lg-12">
                            <label for="event-name" class="form-label form-input-labels">Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name"
                                id="edit_name" name="name">
                        </div>
                        <div class="col-md-12">
                            <label for="startdate" class="form-label form-input-labels">Start Date</label>
                            <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                <input type="text" id="edit_date" class="" placeholder="Select start date"
                                    data-input name="date">
                                <button class="input-button calender-button" type="button" title="toggle" data-toggle>
                                    <i class="fa-regular fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <label for="country-name" class="form-label form-input-labels">Country</label>
                            <select class="form-select form-select-field" id="edit_country_id" name="country_id"
                                data-control="select2" data-placeholder="Select Country">
                                <option selected disabled>Select Country</option>
                                <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($country->id); ?>"><?php echo e($country->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-lg-12">
                            <label for="status" class="form-label form-input-labels">Status</label>
                            <select class="form-select form-select-field" id="edit_status" name="status"
                                data-control="select2" data-placeholder="Select Status">
                                <option selected disabled>Select Status</option>
                                <option value="1">Active</option>
                                <option value="0">Deactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 ">
                    <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/admin/holidays/modal/edit-holiday-modal.blade.php ENDPATH**/ ?>