# Holiday Import Feature Guide

## Overview
The Holiday Import feature allows administrators to bulk import holidays from Excel (.xlsx, .xls) or CSV (.csv) files.

## How to Use

### 1. Access the Import Feature
- Navigate to the Holidays management page
- Click the "Import Holidays" button next to "Add Holidays"

### 2. Prepare Your File
Your import file should have the following structure:

#### CSV Format:
```csv
Holiday Name,Date (YYYY-MM-DD),Status
New Year Day,2024-01-01,active
Independence Day,2024-07-04,active
Christmas Day,2024-12-25,active
```

#### Required Columns:
1. **Holiday Name** (Column 1): The name of the holiday
2. **Date** (Column 2): The date in YYYY-MM-DD format
3. **Status** (Column 3): Optional - defaults to 'active' if not provided

#### Supported Date Formats:
- YYYY-MM-DD (recommended)
- YYYY/MM/DD
- DD-MM-YYYY
- DD/MM/YYYY
- MM-DD-YYYY
- MM/DD/YYYY

### 3. Import Process
1. Click "Import Holidays" button
2. Select your Excel/CSV file
3. Choose the default country for all holidays
4. Click "Import Holidays" to start the process

### 4. Download Sample Template
- Click "Download Sample Template" to get a pre-formatted CSV file
- Use this as a reference for your own import files

## Features

### Duplicate Prevention
- The system checks for existing holidays with the same name, date, and country
- Duplicate entries are skipped and reported in the import summary

### Error Handling
- Invalid date formats are reported and skipped
- Missing required data is handled gracefully
- Import summary shows successful imports and any errors

### File Support
- **CSV files**: Full support with proper parsing
- **Excel files**: Basic support (currently processed as CSV)
- Maximum file size: 2MB

## Permissions
- Requires 'holidays-create' permission to access import functionality
- Same permission level as adding individual holidays

## Tips for Best Results

1. **Use the sample template** as a starting point
2. **Ensure dates are in YYYY-MM-DD format** for best compatibility
3. **Remove any extra columns** not specified in the format
4. **Check for duplicates** in your file before importing
5. **Keep file size under 2MB** for optimal performance

## Troubleshooting

### Common Issues:
- **File format not supported**: Ensure file has .csv, .xlsx, or .xls extension
- **Date format errors**: Use YYYY-MM-DD format or other supported formats
- **Permission denied**: Contact administrator to ensure you have 'holidays-create' permission
- **Duplicate entries**: Check existing holidays before importing

### Error Messages:
- "Invalid date format": Check your date column format
- "Holiday already exists": The holiday with same name/date/country already exists
- "Validation Errors": Check required fields are filled

## Sample Data
A sample CSV file is available at `/public/sample_holidays.csv` for testing purposes.
