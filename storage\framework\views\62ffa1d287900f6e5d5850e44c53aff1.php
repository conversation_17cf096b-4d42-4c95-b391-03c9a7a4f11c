<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Manage Holidays</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holidays-create')): ?>
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#import-holidays">
                            <i class="fa-solid fa-upload me-3"></i> Import Holidays
                        </a>
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-holiday">
                            <i class="fa-solid fa-plus me-3"></i> Add Holidays
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <table id="responsiveTable"
                            class="responsiveTable manage-holiday vat-managment-table display w-100">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td data-label="EVENT TITLE"><?php echo e($holiday->name ?? ''); ?></td>
                                        <td data-label="DATE"><?php echo e($holiday->date); ?></td>
                                        <td data-label="">
                                            <div class="dropdown d-flex justify-content-center">
                                                <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holidays-edit')): ?>
                                                    <li>
                                                        <button class="dropdown-item complete fs-14 regular edit-holiday "
                                                            type="button" data-id="<?php echo e($holiday->ids); ?>">
                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                            Edit
                                                        </button>
                                                    </li>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holidays-delete')): ?>
                                                    <li>
                                                        <form action="<?php echo e(route('holidays.destroy', $holiday->ids)); ?>"
                                                            method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button class="dropdown-item cancel fs-14 regular"
                                                                type="submit">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                Delete
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('dashboard.admin.holidays.modal.add-holiday-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.admin.holidays.modal.edit-holiday-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.admin.holidays.modal.import-holidays-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#holidayForm").validate({
                rules: {
                    name: {
                        required: true
                    },
                    date: {
                        required: true
                    },
                    country_id: {
                        required: true
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    name: {
                        required: "Please enter holiday name"
                    },
                    date: {
                        required: "Please select date"
                    },
                    country_id: {
                        required: "Please select country"
                    },
                    status: {
                        required: "Please select status"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

        <script>
        $(document).ready(function() {

            $('.edit-holiday').click(function() {
                var holidayId = $(this).data('id');
                $.ajax({
                    url: '/holidays/' + holidayId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#holidayId').val(data.ids);
                        $('#edit_name').val(data.name);
                        $('#edit_date').val(data.date);
                        $('#edit_country_id').val(data.country_id);
                        $('#edit_status').val(data.status);
                    },
                    complete: function() {
                        $('#edit-holiday').modal('show');
                    },
                    error: function(xhr) {
                        alert('Failed to fetch holiday data. Please try again.');
                    }
                });
            });

            $('#editHolidayForm').on('submit', function(e) {
                e.preventDefault();
                var holidayId = $('#holidayId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/holidays/' + holidayId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-holiday').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });

        // Handle import form submission
        $('#importHolidaysForm').on('submit', function(e) {
            e.preventDefault();

            var formData = new FormData(this);
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();

            // Show loading state
            submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i>Importing...');
            submitBtn.prop('disabled', true);

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#import-holidays').modal('hide');
                    Swal.fire({
                        icon: response.type || 'success',
                        title: response.title || 'Success',
                        text: response.message || 'Import completed successfully'
                    });
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    Swal.fire({
                        icon: 'error',
                        title: 'Import Failed',
                        text: response?.message || 'Import failed. Please try again.'
                    });
                },
                complete: function() {
                    // Reset button state
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/admin/holidays/holidays.blade.php ENDPATH**/ ?>