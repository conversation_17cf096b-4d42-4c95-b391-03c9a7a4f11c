<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoriesController;
use App\Http\Controllers\CmsController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DiscountCouponsController;
use App\Http\Controllers\EmailVerificationController;
use App\Http\Controllers\GoogleCalendarController;
use App\Http\Controllers\ProfessionalController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SubCategoriesController;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\StripeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::get("/clear-all", [WebsiteController::class, "clear_all"]);
Route::get('/', [WebsiteController::class, 'index'])->name('home');

Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});



// Zohaib Route
Route::get('send-otp', [EmailVerificationController::class, 'sendOtp'])->name('send_otp');
Route::post('verify-otp', [EmailVerificationController::class, 'verifyOtp'])->name('verify_otp');
Route::post("set-password", [AuthController::class, "set_password"])->name('set_password');

Route::middleware(['auth', "verified"])->group(function () {
    Route::get("register/{user_type}", [AuthController::class, 'registerUserType'])->name('register.user_type')->where("user_type", "customer|professional");
    Route::post("register/customer", [AuthController::class, 'registerCustomer'])->name('register.customer');

    // Professional registration step-wise routes
    Route::post("register/professional/save-step", [AuthController::class, 'saveStepData'])->name('register.professional.save_step');
    Route::get("register/professional/get-progress", [AuthController::class, 'getProgress'])->name('register.professional.get_progress');
});

Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', [ThemeController::class, 'permissions'])->name('permissions')->middleware('auth');
Auth::routes();
Route::get('/home', [ThemeController::class, 'dashboard'])->name('dashboard')->middleware('auth');

// Route::fallback(function(){
// route(404);
// });
// Google OAuth Routes
//Route::get("register/{user_type}", [AuthController::class, 'registerUserType'])->name('register.user_type')->where("user_type", "customer|professional");
// Social login routes with user type parameter
Route::get('/auth/google/{user_type}', [SocialAuthController::class, 'redirectToGoogle'])->name('google.login.with.type')->where("user_type", "customer|professional");
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback');

Route::get('/auth/apple/{user_type}', [SocialAuthController::class, 'redirectToApple'])->name('apple.login.with.type')->where("user_type", "customer|professional");
Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback'])->name('apple.callback');

// Google Calendar Routes
Route::get('/google-calendar/connect', [GoogleCalendarController::class, 'redirectToGoogle'])->name('google.calendar.connect');
Route::get('/google-calendar/callback', [GoogleCalendarController::class, 'handleGoogleCallback']);
Auth::routes(['verify' => true]);
Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

// Route::middleware(['auth', "verified"])->group(function () {
//     Route::get("dashboard", function () {
//         return "the email is verified";
//     });
//     Route::get('/dashboard', [ThemeController::class, 'dashboard'])->name('dashboard');
// });

Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    //Route::resource('roles/{id?}', RoleController::class)->name('roles.edit');
    Route::resource('users', UserController::class);
});

// website  route
Route::get('/all-services', [WebsiteController::class, 'services'])->name('website_services');
Route::get('professional', [WebsiteController::class, 'professional'])->name('professional');
Route::get('privacy_policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy_policy');
Route::get('terms', [WebsiteController::class, 'terms'])->name('terms');

//customer routes
Route::group(['middleware' => ['auth', 'role:customer','verified','user_check']], function () {
    Route::get('family_friends', [ThemeController::class, 'familyFriends'])->name('family_friends');
    Route::get('family_friends_details', [ThemeController::class, 'friendsDetails'])->name('family_friends_details');
    Route::get('add-friends', [ThemeController::class, 'addFriends'])->name('add-friends');
    Route::get('customer_booking', [ThemeController::class, 'customerBooking'])->name('customer_booking');
    Route::get('customer_wallet', [ThemeController::class, 'customerWallet'])->name('customer_wallet');
    Route::get('favorite_professional', [ThemeController::class, 'favoriteProfessional'])->name('favorite_professional');
    Route::get('cart', [ThemeController::class, 'cart'])->name('cart');
    Route::get('professional_profile', [ThemeController::class, 'professional_profile'])->name('professional_profile');
    Route::get('profile_setting',[ThemeController::class, 'profileSetting'])->name('profile_setting');
});

//Businesss,individual and professional routes
Route::group(['middleware' => ['auth','verified','user_check', 'professional.restrict', 'role:admin|business|individual|professional']], function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('booking', [ThemeController::class, 'businessBooking'])->name('booking');
    Route::controller(StripeController::class)->group(function () {
        Route::post('/payment/subscription',  'purchaseSubscription')->name('payment.subscription');
        Route::get('/subscription/success',  'subscriptionSuccess')->name('subscription.success');
        Route::get('/subscription/failed',  'subscriptionFailed')->name('subscription.failed');
        Route::post('/subscription/cancel',  'cancel_subscription')->name('subscription.cancel');
    });
    Route::get('setting', [ThemeController::class, 'setting'])->name('setting');
    Route::get('profile_setting',[ThemeController::class, 'profileSetting'])->name('profile_setting');
    Route::resource("subscriptions", "\App\Http\Controllers\SubscriptionController");

    Route::group(['middleware' => ['role:admin|business|individual|professional']], function () {
        // Service 
        Route::prefix('services')->group(function () {
            Route::get('/', [ServiceController::class, 'index'])->name('services.index');
            Route::get('/create/{type}', [ServiceController::class, 'create'])->name('services.create');
            Route::post('/store/{type}', [ServiceController::class, 'store'])->name('services.store');
            Route::get('/{service}/edit/{type}', [ServiceController::class, 'edit'])->name('services.edit');
            Route::put('/{service}/update/{type}', [ServiceController::class, 'update'])->name('services.update');
            Route::delete('/{service}/delete', [ServiceController::class, 'destroy'])->name('services.destroy');
        });
        // Subcategory
        Route::prefix('subcategories')->group(function () {
            Route::get('/get-subcategories/{category_id}', [CategoriesController::class, 'getSubCategories'])->name('subcategories.get');
        });

        Route::get('business_analytics', [ThemeController::class, 'businessAnalytics'])->name('business_analytics');
        Route::get('add_staff_member', [ThemeController::class, 'addStaffMember'])->name('add_staff_member');
        Route::get('earning', [ThemeController::class, 'businessEarning'])->name('earning');
        Route::get('staff_member', action: [ThemeController::class, 'staffMember'])->name('staff_member');
        Route::get('staff-member-details', [ThemeController::class, 'staffMemberDetails'])->name('staff-member-details');
        Route::get('notification', action: [ThemeController::class, 'notification'])->name('notification');
    });
});

//Stepper form route
Route::get('professional_account', [ThemeController::class, 'professional_account'])->name('professional_account_stepper');
Route::get('customer/testing', [ThemeController::class, 'testing'])->name('customer.testing');

// admin routes
Route::group(['middleware' => ['auth', 'role:admin', 'verified']], function () {
    Route::prefix('professionals')->group(function () {
        Route::get("/", [ProfessionalController::class, 'index'])->name('professionals');
        Route::get('approve/{id}', [ProfessionalController::class, 'approve'])->name('professional.approve');
        Route::get('change_status/{id}', [ProfessionalController::class, 'changeStatus'])->name('professional.change_status');
        // Route::get('reject/{id}', [ProfessionalController::class, 'reject'])->name('professional.reject');
    });
    Route::get('customers', [DashboardController::class, 'adminCustomers'])->name('customers');
    Route::get('customers/change_status/{id}', [DashboardController::class, 'changeStatus'])->name('customer.change_status');
    Route::get('refund_request', [ThemeController::class, 'refundRequest'])->name('refund_request');
    Route::get('wallet', [ThemeController::class, 'adminWallet'])->name('wallet');

    Route::get('/cms/home', [CmsController::class, 'home'])->name('cms.home');
    Route::post('/cms/home', [CmsController::class, 'editHome'])->name('cms.home.edit');
    Route::get('/cms/privacy', [CmsController::class, 'privacy'])->name('cms.privacy');
    Route::get('/cms/terms', [CmsController::class, 'terms'])->name('cms.terms');
    Route::post('/cms/privacy-terms', [CmsController::class, 'editPrivacyTerms'])->name('cms.privacy-terms.edit');

    Route::resource("categories", "\App\Http\Controllers\CategoriesController");
    Route::resource("subcategories", "\App\Http\Controllers\SubCategoriesController");
    Route::post('/categories/update-status', [CategoriesController::class, 'updateStatus'])->name('categories.update-status');
    Route::post('/subcategories/update-status', [SubCategoriesController::class, 'subUpdateStatus'])->name('subcategories.update-status');
    Route::resource("certifications", "\App\Http\Controllers\CertificationsController");
    Route::resource("holidays", "\App\Http\Controllers\HolidaysController");
    Route::post('holidays/import', [\App\Http\Controllers\HolidaysController::class, 'import'])->name('holidays.import');
    Route::get('holidays/download-template', [\App\Http\Controllers\HolidaysController::class, 'downloadTemplate'])->name('holidays.download-template');
    Route::resource("vatmanagements", "\App\Http\Controllers\VatManagementsController");
    Route::resource("discount-coupons", "\App\Http\Controllers\DiscountCouponsController");
    Route::post('discount-coupons/check-coupon-code', [DiscountCouponsController::class, 'checkCouponCode'])->name('discountcoupons.check-coupon-code');
    Route::post('discount-coupons/update-status', [DiscountCouponsController::class, 'updateStatus'])->name('discount-coupons.update-status');
    Route::resource("countries", "\App\Http\Controllers\CountriesController");
    Route::resource("settings", "\App\Http\Controllers\SettingsController");
});

// Google OAuth Routes
// Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('google.login');
// Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback']);

// Apple OAuth Routes
// Route::get('/auth/apple', [SocialAuthController::class, 'redirectToApple'])->name('apple.login');
// Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback']);

Route::get('/auth/apple', [SocialAuthController::class, 'redirectToApple'])->name('apple.login');
Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback']);

// Google Calendar Routes
Route::get('/google-calendar/connect', [GoogleCalendarController::class, 'redirectToGoogle'])->name('google.calendar.connect');
Route::get('/google-calendar/callback', [GoogleCalendarController::class, 'handleGoogleCallback']);
//Stripe Webhook
Route::post('/stripe/webhook', [StripeController::class, 'handleStripeWebhook'])->name('stripe.webhook');


