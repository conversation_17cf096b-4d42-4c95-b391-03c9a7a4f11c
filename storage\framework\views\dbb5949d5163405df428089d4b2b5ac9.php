<div class="modal fade card-details" id="add-holiday" tabindex="-1" aria-labelledby="add-holiday" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black">
                    Add a Holiday</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="holidayForm" action="<?php echo e(route('holidays.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-lg-12">
                            <label for="event-name" class="form-label form-input-labels">Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name"
                                id="name" name="name" value="<?php echo e(old('name')); ?>">
                        </div>
                        <div class="col-md-12">
                            <label for="startdate" class="form-label form-input-labels">Date</label>
                            <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                <input type="text" id="date" class="" placeholder="Select start date"
                                    data-input name="date" value="<?php echo e(old('date')); ?>">
                                <button class="input-button calender-button" type="button" title="toggle" data-toggle>
                                    <i class="fa-regular fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <label for="country-name" class="form-label form-input-labels">Country</label>
                            <select class="form-select form-select-field" id="country_id" name="country_id"
                                data-control="select2" data-placeholder="Select Country">
                                <option selected disabled>Select Country</option>
                                <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($country->id); ?>" <?php echo e(old('country_id') == $country->id ? 'selected' : ''); ?>>
                                        <?php echo e($country->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 ">
                    <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/admin/holidays/modal/add-holiday-modal.blade.php ENDPATH**/ ?>