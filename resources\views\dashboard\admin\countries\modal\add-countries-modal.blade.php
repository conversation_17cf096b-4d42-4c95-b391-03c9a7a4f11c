<div class="modal fade card-details" id="add-certification" tabindex="-1" aria-labelledby="add-certification"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black">
                    Add Product Certification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="countriesForm" action="{{ route('countries.store') }}" enctype="multipart/form-data"
                method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row row-gap-5 ">
                        <div class="col-lg-12">
                            <label for="country-name" class="form-label form-input-labels">Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name"
                                id="name" name="name" value="{{ old('name') }}">
                        </div>
                    </div>
                    <div class="row row-gap-5 ">
                        <div class="col-lg-12">
                            <label for="country-code" class="form-label form-input-labels">Country Code</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name"
                                id="country_code" name="country_code" value="{{ old('country_code') }}">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 px-0">
                    <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>
