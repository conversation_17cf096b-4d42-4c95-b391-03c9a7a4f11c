<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ProfessionalsExport;

class ProfessionalController extends Controller
{
    function index(){

        $approved_users = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("approval", 1)->where("registration_completed", 1)->get();
        
        $unapproved_users = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional']);
        })->where("approval", 0)->where("registration_completed", 1)->get();


        return view('professional.index', compact('unapproved_users', 'approved_users'));
    }

    function approve($ids){
        $user = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional']);
        })->where('ids', $ids)->firstOrFail();
        if($user->approval == 1){
            return redirect()->back()->with(['title'=>'Error','message'=>'Professional already approved','type'=>'error']);
        }
        $user->approval = 1;
        $user->save();
        return redirect()->back()->with(['title'=>'Done','message'=>'Professional approved successfully','type'=>'success']);
    }
    function changeStatus($ids){
        $user = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where('ids', $ids)->firstOrFail();

        if($user->status == 1){
            $user->status = 0;
            $message = 'Professional deactivated successfully';
        }else{
            $user->status = 1;
            $message = 'Professional activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title'=>'Done','message'=>$message,'type'=>'success']);
    }

    /**
     * Export approved professionals to Excel
     */
    public function exportApproved()
    {
        $approved_users = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("approval", 1)->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($approved_users, 'approved'), 'approved_professionals_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export unapproved professionals to Excel
     */
    public function exportUnapproved()
    {
        $unapproved_users = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional']);
        })->where("approval", 0)->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($unapproved_users, 'unapproved'), 'unapproved_professionals_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export all professionals to Excel
     */
    public function exportAll()
    {
        $all_users = User::whereHas('roles', function($q){
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($all_users, 'all'), 'all_professionals_' . date('Y-m-d') . '.xlsx');
    }
}
